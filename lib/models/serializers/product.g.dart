// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SerializerProduct _$SerializerProductFromJson(Map<String, dynamic> json) =>
    SerializerProduct(
      id: json['id'] as int?,
      title: json['title'] as String?,
      titleAr: json['title_ar'] as String?,
      isOutOfStock: json['is_out_of_stock'] as bool?,
      inventory: json['inventory'] as int?,
      price: (json['price'] as num?)?.toDouble() ?? 0,
      salePrice: (json['sale_price'] as num?)?.toDouble() ?? 0,
      description: json['description'] as String?,
      descriptionAr: json['description_ar'] as String?,
      inventoryEnabled: (json['inventory_enabled'] as bool?) ?? false,
      isSizeColorInventory: (json['is_size_color_inventory'] as bool?) ?? false,
      isFeatured: json['is_featured'] as bool?,
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => Image.fromJson(e as Map<String, dynamic>))
          .toList(),
      colors: List<ExtraSettingsModel>.from((json['colors'] ?? [])
          .map((e) => ExtraSettingsModel.fromJson(e as Map<String, dynamic>))
          .toList()),
      sizes: List<ExtraSettingsModel>.from((json['sizes'] ?? [])
          .map((e) => ExtraSettingsModel.fromJson(e as Map<String, dynamic>))
          .toList()),
      thumbnail: json['thumbnail'] == null
          ? null
          : Thumbnail.fromJson(json['thumbnail'] as Map<String, dynamic>),
      productCategories: (json['categories'] as List<dynamic>?)
          ?.map((e) =>
              SerializerProductCategory.fromJson(e as Map<String, dynamic>))
          .toList(),
      review: json['review'] as int?,
      isSale: json['is_sale'] as bool? ?? false,
      minQuantitySaleNumber: json['min_quantity_sale_number'] as int?,
      quantitySale: json['quantity_sale'] as num?,
      isQuantitySalePercentage: json['is_quantity_sale_percentage'] as bool?,
    );

Map<String, dynamic> _$SerializerProductToJson(SerializerProduct instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'title_ar': instance.titleAr,
      'is_out_of_stock': instance.isOutOfStock,
      'inventory_enabled': instance.inventoryEnabled,
      'is_size_color_inventory': instance.isSizeColorInventory,
      'description': instance.description,
      'description_ar': instance.descriptionAr,
      'inventory': instance.inventory,
      'price': instance.price,
      'sale_price': instance.salePrice,
      'images': instance.images?.map((e) => e.toJson()).toList(),
      'thumbnail': instance.thumbnail?.toJson(),
      'review': instance.review,
      'is_sale': instance.isSale,
      'is_featured': instance.isFeatured,
      'categories': instance.productCategories?.map((e) => e.toJson()).toList(),
      'min_quantity_sale_number': instance.minQuantitySaleNumber,
      'quantity_sale': instance.quantitySale,
      'is_quantity_sale_percentage': instance.isQuantitySalePercentage,
    };
