import '../../common/constants.dart';
import 'country_model.dart';
import 'extra_setting_model.dart';
import 'checkout_settings_model.dart';
import 'currency_model.dart';

class ConfigModel {
  final int? id;
  final String? documentId;
  final int? vendorId;
  final List<CurrencyModel> currencies;
  final List<CountryModel> countries;
  final List<ExtraSettingsModel> sizes;
  final List<ExtraSettingsModel> colors;
  final int bannerLimit;
  final bool showPricing;
  final bool isActiveWorkingTime;
  final num? minimumOrderCost;
  final bool? orderRingingBell;
  final String? primaryColor;
  final String? defaultLanguage;
  final String? defaultTheme;
  final CheckoutSettingsModel? checkoutSettings;
  final bool showMap;
  final bool showBoundaries;

  ConfigModel({
    this.id,
    this.showMap = false,
    this.showBoundaries = false,
    this.documentId,
    this.vendorId,
    this.minimumOrderCost,
    this.currencies = const [],
    this.sizes = const [],
    this.colors = const [],
    this.countries = const [],
    this.bannerLimit = 0,
    this.showPricing = true,
    this.isActiveWorkingTime = true,
    this.orderRingingBell,
    this.primaryColor,
    this.defaultLanguage,
    this.defaultTheme,
    this.checkoutSettings,
  });

  factory ConfigModel.fromJson(Map<String, dynamic> json) {
    printLog('FSAFSF ${json['show_map']}');

    return ConfigModel(
      id: json['id'],
      documentId: json['documentId'],
      showBoundaries: json['show_boundaries'] ?? false,
      showMap: json['show_map'] ?? false,
      isActiveWorkingTime: json['is_active_working_time'] ?? true,
      primaryColor: json['primary_color'],
      defaultLanguage: json['default_language'] ?? 'en',
      minimumOrderCost: json['minimum_order_cost'] ?? 0,
      bannerLimit: json['banner_limit'] ?? 0,
      countries: json['countries'] != null
          ? (json['countries'] as List)
              .map((e) => CountryModel.fromJson(e))
              .toList()
          : [],
      currencies: json['currencies'] != null
          ? (json['currencies'] as List)
              .map((e) => CurrencyModel.fromJson(e))
              .toList()
          : [],
      sizes: json['sizes'] != null
          ? (json['sizes'] as List)
              .map((e) => ExtraSettingsModel.fromJson(e))
              .toList()
          : [],
      colors: json['colors'] != null
          ? (json['colors'] as List)
              .map((e) => ExtraSettingsModel.fromJson(e))
              .toList()
          : [],
      defaultTheme: json['default_theme'] ?? 'light',
      showPricing: json['show_pricing'] ?? true,
      orderRingingBell: json['order_ringing_bell'],
      checkoutSettings: json['checkout_settings'] != null
          ? CheckoutSettingsModel.fromJson(json['checkout_settings'])
          : null,
    );
  }

  ConfigModel copyWith({
    int? id,
    String? documentId,
    int? vendorId,
    String? primaryColor,
    String? defaultLanguage,
    String? defaultTheme,
    num? minimumOrderCost,
    List<CurrencyModel>? currencies,
    List<ExtraSettingsModel>? sizes,
    List<ExtraSettingsModel>? colors,
    List<CountryModel>? countries,
    int? bannerLimit,
    bool? showPricing,
    bool? isActiveWorkingTime,
    bool? orderRingingBell,
    CheckoutSettingsModel? checkoutSettings,
    bool? showMap,
    bool? showBoundaries,
  }) {
    return ConfigModel(
      id: id ?? this.id,
      documentId: documentId ?? this.documentId,
      vendorId: vendorId ?? this.vendorId,
      minimumOrderCost: minimumOrderCost ?? this.minimumOrderCost,
      currencies: currencies ?? this.currencies,
      primaryColor: primaryColor ?? this.primaryColor,
      defaultLanguage: defaultLanguage ?? this.defaultLanguage,
      defaultTheme: defaultTheme ?? this.defaultTheme,
      sizes: sizes ?? this.sizes,
      colors: colors ?? this.colors,
      countries: countries ?? this.countries,
      bannerLimit: bannerLimit ?? this.bannerLimit,
      showPricing: showPricing ?? this.showPricing,
      isActiveWorkingTime: isActiveWorkingTime ?? this.isActiveWorkingTime,
      orderRingingBell: orderRingingBell ?? this.orderRingingBell,
      checkoutSettings: checkoutSettings ?? this.checkoutSettings,
      showMap: showMap ?? this.showMap,
      showBoundaries: showBoundaries ?? this.showBoundaries,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'showMap': showMap,
      'showBoundaries': showBoundaries,
      'documentId': documentId,
      'vendorId': vendorId,
      'primaryColor': primaryColor,
      'bannerLimit': bannerLimit,
      'showPricing': showPricing,
      'isActiveWorkingTime': isActiveWorkingTime,
      'orderRingingBell': orderRingingBell,
      'checkoutSettings': checkoutSettings?.toJson(),
    };
  }
}
