import '../../frameworks/strapi/services/strapi_service.dart';

class ExtraSettingsModel {
  final int? id;
  final String name;
  final int? stock;
  final num? price;

  bool get isOutOfStock => stock != null && stock! <= 0;

  const ExtraSettingsModel({
    this.id,
    this.name = '',
    this.stock,
    this.price,
  });

  factory ExtraSettingsModel.fromJson(Map<String, dynamic> json) {
    final name = translatedText(textEn: json['name'], textAr: json['name_ar']);

    return ExtraSettingsModel(
      id: json['id'],
      name: name,
      stock: int.tryParse(json['stock']?.toString() ?? ''),
      price: num.tryParse(json['price']?.toString() ?? ''),
    );
  }

  factory ExtraSettingsModel.fromProductJson(Map<String, dynamic> json) {
    final name = translatedText(textEn: json['name'], textAr: json['name_ar']);

    return ExtraSettingsModel(
      name: name,
      stock: int.tryParse(json['stock'] ?? ''),
      price: json['price'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      if (stock != null) 'stock': stock,
      if (price != null) 'price': price,
    };
  }

  //! Copy With
  ExtraSettingsModel copyWith({
    int? id,
    String? name,
    num? price,
  }) {
    return ExtraSettingsModel(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
    );
  }

  @override
  String toString() {
    return toJson().toString();
  }
}

class SelectedSizeAndColor {
  final String? size;
  final String? color;
  final num? price;
  int? quantity;

  SelectedSizeAndColor({
    this.size,
    this.color,
    this.quantity,
    this.price,
  });

  factory SelectedSizeAndColor.fromJson(Map<String, dynamic> json) {
    return SelectedSizeAndColor(
      size: json['size'] ?? '',
      color: json['color'] ?? '',
      quantity: json['quantity'] ?? 1,
      price: json['price'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'size': size,
      'color': color,
      'quantity': quantity,
      'price': price,
    };
  }

  //! Copy With
  SelectedSizeAndColor copyWith({
    String? size,
    String? color,
    int? quantity,
    num? price,
  }) {
    return SelectedSizeAndColor(
      size: size ?? this.size,
      color: color ?? this.color,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
    );
  }

  @override
  String toString() {
    return toJson().toString();
  }
}
