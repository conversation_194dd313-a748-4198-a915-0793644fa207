import 'dart:convert';
import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:html_unescape/html_unescape.dart';
import 'package:quiver/strings.dart';

import '../../common/config.dart';
import '../../common/constants.dart';
import '../../data/boxes.dart';
import '../../frameworks/strapi/services/strapi_service.dart';
import '../../services/service_config.dart';
import '../../services/services.dart';
import '../booking/booking_model.dart';
import '../vendor/extra_setting_model.dart';
import '../serializers/product.dart';
import '../serializers/product_category.dart';
import 'brand.dart';
import 'category.dart';
import 'listing_hour.dart';
import 'listing_slots.dart';
import 'menu_price.dart';
import 'product_addons.dart';
import 'product_attribute.dart';
import 'product_component.dart';
import 'product_variation.dart';
import 'store/store.dart';
import 'tag.dart';
import 'user.dart';
import 'vendor_admin_variation.dart';

const _defaultId = '0';

class Product {
  String id;
  String? sku;
  String? name;
  String? nameEn;
  String? nameAr;
  String? status;
  String? vendor;
  String? description;
  String? shortDescription;
  String? permalink;
  String? price;
  String? regularPrice;
  String? salePrice;
  String? wholesalePrice;
  String? maxPrice;
  String? minPrice;
  bool? onSale;
  bool? inStock;
  double? averageRating;
  int? totalSales;
  String? dateOnSaleFrom;
  String? dateOnSaleTo;
  String? selectedSize;
  String? selectedColor;
  int? ratingCount;
  List<String> images = [];
  String? imageFeature;
  List<ProductAttribute>? attributes;
  Map<String?, String?> attributeSlugMap = {};
  List<Attribute> defaultAttributes = <Attribute>[];
  List<ProductAttribute> infors = [];
  String? categoryId;
  String? videoUrl;
  List<dynamic>? groupedProducts;
  List<String?>? fileNames;
  List<String?>? files;
  int? stockQuantity;
  int? minQuantity;
  int? maxQuantity;
  int? quantityStep;
  bool manageStock = false;
  bool backOrdered = false;
  String? relatedIds;
  bool backordersAllowed = false;
  bool inventoryEnabled = false;
  bool isSizeColorInventory = false;
  List<Tag> tags = [];
  Map<String, Map<String, AddonsOption>> defaultAddonsOptions = {};
  List<Category> categories = [];
  SerializerProductCategory? productCategory;
  List<Map> metaData = [];
  bool isRestricted = false; //for Indeed Ultimate Membership Pro

  List<Brand> brands = [];

  List<ProductAddons>? addOns;
  List<AddonsOption>? selectedOptions;
  List<VendorAdminVariation> vendorAdminVariations = [];

  List<ProductVariation>? variationProducts;

  /// For downloadable products
  bool isPurchased = false;
  bool? isDownloadable = false;

  /// is to check the type affiliate, simple, variant
  String? type;
  String? affiliateUrl;
  List<ProductVariation>? variations;

  bool get isVariableType => type == 'variable';
  List<Map>? options; //for opencart
  bool get isSimpleType => type == 'simple';

  bool get isNofoundType => type == '';
  BookingModel? bookingInfo; // for booking

  String? idShop; //for prestashop

  ///----VENDOR ADMIN----///
  bool? isFeatured = false;
  String? vendorAdminImageFeature;
  List<String> categoryIds = [];
  List<ProductAttribute> vendorAdminProductAttributes = [];

  List<ExtraSettingsModel> sizes = [];
  List<ExtraSettingsModel> colors = [];
  Store? store;

  ///----VENDOR ADMIN----///

  ///----FLUXSTORE LISTING----///

  String? distance;
  Map? pureTaxonomies;
  List? reviews;
  String? featured;
  bool? verified;
  String? tagLine;
  String? priceRange;
  String? categoryName;
  String? hours;
  String? location;
  String? phone;
  String? facebook;
  String? email;
  String? website;
  String? skype;
  String? whatsapp;
  String? youtube;
  String? twitter;
  String? eventDate;
  ListingHour? listingHour;
  String? instagram;

  // Determine the product or listing type
  bool listingType = false;

  // String? rating;
  int? totalReview = 0;
  double? lat;
  double? long;
  List<dynamic>? listingMenu = [];
  ListingSlots? slots;
  bool? listingBookingStatus;

  String? mVideoUrl;
  String? mVideoTitle;
  String? mVideoDesc;
  List<String>? giftCardAmounts;
  bool? cpPerItemPricing;

  bool get availableRating => averageRating != null && averageRating != 0.0;

  List<ProductComponent>? components;

  int? minQuantitySaleNumber;
  num? quantitySale;
  bool? isQuantitySalePercentage;

  bool _hasWholesalePrice(User user, {bool? isSale}) {
    var haveWholesalePriceKey = isSale == true
        ? '${user.role ?? ''}_have_wholesale_sale_price'
        : '${user.role ?? ''}_have_wholesale_price';

    var haveWholesalePriceMeta = _getMetaData(haveWholesalePriceKey);
    return haveWholesalePriceMeta.isNotEmpty &&
        haveWholesalePriceMeta['value'] == 'yes';
  }

  ///----FLUXSTORE LISTING----///
  Product(
      {this.id = _defaultId,
      String? sku,
      String? name,
      String? nameEn,
      String? nameAr,
      String? selectedSize,
      String? selectedColor,
      String? status,
      String? vendor,
      String? description,
      String? shortDescription,
      String? permalink,
      String? price,
      String? regularPrice,
      String? salePrice,
      String? wholesalePrice,
      String? maxPrice,
      String? minPrice,
      bool? onSale,
      bool? inStock,
      SerializerProductCategory? productCategory,
      double? averageRating,
      int? totalSales,
      String? dateOnSaleFrom,
      String? dateOnSaleTo,
      int? ratingCount,
      List<String>? images,
      String? imageFeature,
      List<ProductAttribute>? attributes,
      List<Attribute>? defaultAttributes,
      List<ProductAttribute>? infors,
      String? categoryId,
      String? videoUrl,
      List<dynamic>? groupedProducts,
      List<String>? fileNames,
      List<String?>? files,
      int? stockQuantity,
      int? minQuantity,
      int? maxQuantity,
      int? quantityStep,
      bool? manageStock,
      bool? backOrdered,
      String? relatedIds,
      bool? backordersAllowed,
      List<Tag>? tags,
      Store? store,
      List<Category>? categories,
      List<Map>? metaData,
      List<ProductAddons>? addOns,
      List<AddonsOption>? selectedOptions,
      List<VendorAdminVariation>? vendorAdminVariations,
      List<ProductVariation>? variationProducts,
      bool? isPurchased,
      bool? isDownloadable,
      String? type,
      String? affiliateUrl,
      List<ProductVariation>? variations,
      List<Map>? options,
      BookingModel? bookingInfo,
      String? idShop,
      bool? isFeatured,
      String? vendorAdminImageFeature,
      List<String>? categoryIds,
      List<ProductAttribute>? vendorAdminProductAttributes,
      String? distance,
      Map? pureTaxonomies,
      List? reviews,
      String? featured,
      bool? verified,
      String? tagLine,
      String? priceRange,
      String? categoryName,
      String? hours,
      String? location,
      String? phone,
      String? facebook,
      String? email,
      String? website,
      String? skype,
      String? whatsapp,
      String? youtube,
      String? twitter,
      String? eventDate,
      String? rating,
      int? totalReview,
      double? lat,
      double? long,
      List<dynamic>? listingMenu,
      ListingSlots? slots,
      bool? isRestricted,
      bool? listingBookingStatus,
      bool? inventoryEnabled,
      bool? isSizeColorInventory,
      ListingHour? listingHour,
      String? instagram,
      String? mVideoUrl,
      String? mVideoTitle,
      String? mVideoDesc,
      List<Brand>? brands,
      List<ExtraSettingsModel>? sizes,
      List<ExtraSettingsModel>? colors,
      List<String>? giftCardAmounts,
      bool? cpPerItemPricing});

  Product.empty(this.id) {
    name = '';
    nameAr = '';
    price = '0.0';
    imageFeature = '';
  }

  bool isEmptyProduct() {
    return name == '' && nameAr == '' && price == '0.0' && imageFeature == '';
  }

  bool isTopUpProduct() {
    return name == 'Wallet Topup';
  }

  bool canBeAddedToCartFromList({bool? enableBottomAddToCart}) {
    final isEnableBottomCard =
        enableBottomAddToCart ?? kAdvanceConfig.enableBottomAddToCart;

    final isPassedType = [
          'external',
          'booking',
          'grouped',
          if (isEnableBottomCard == false) ...[
            'variable',
            'appointment',
          ],
        ].contains(type) ==
        false;

    final isCanAdd = !isEmptyProduct() &&
        ((inStock != null && inStock!) || backordersAllowed) &&
        isPassedType &&
        (addOns?.isEmpty ?? true) &&
        (options?.isEmpty ?? true);

    return isCanAdd;
  }

  bool get isVariableProduct => type == 'variable';

  bool get isSimpleProduct => type == 'simple';

  bool get isGroupedProduct => type == 'grouped';

  String? get displayPrice {
    return onSale == true
        ? (isNotBlank(salePrice) ? salePrice ?? '0' : price)
        : (isNotBlank(regularPrice) ? regularPrice ?? '0' : price);
  }

  Map _getMetaData(String key) {
    return metaData.firstWhere(
      (item) => item['key'] == key,
      orElse: () => {},
    );
  }

  bool get isGiftCardProduct => type == 'gift-card';

  bool get isCompositeProduct => type == 'yith-composite';

  bool get isPWGiftCardProduct =>
      type == 'pw-gift-card' && kAdvanceConfig.enablePWGiftCard;

  List<Category> get distinctCategories {
    final temp = categories.map((e) => e.name).toSet().toList();
    return temp
        .map((e) => categories.firstWhere((element) => element.name == e))
        .toList();
  }

  Product.cloneFrom(Product p) : id = p.id {
    giftCardAmounts = p.giftCardAmounts;

    sku = p.sku;
    status = p.status;
    name = p.name;
    nameAr = p.nameAr;
    description = p.description;
    permalink = p.permalink;
    price = p.price;
    regularPrice = p.regularPrice;
    salePrice = p.salePrice;
    productCategory = p.productCategory;
    wholesalePrice = p.wholesalePrice;
    onSale = p.onSale;
    inStock = p.inStock;
    averageRating = p.averageRating;
    ratingCount = p.ratingCount;
    totalSales = p.totalSales;
    dateOnSaleFrom = p.dateOnSaleFrom;
    dateOnSaleTo = p.dateOnSaleTo;
    images = p.images.toList();
    imageFeature = p.imageFeature;
    attributes = p.attributes?.toList();
    infors = p.infors.toList();
    categoryId = p.categoryId;
    videoUrl = p.videoUrl;
    nameEn = p.nameEn;
    nameAr = p.nameAr;
    groupedProducts = p.groupedProducts?.toList();
    fileNames = p.fileNames?.toList();
    files = p.files?.toList();
    stockQuantity = p.stockQuantity;
    minQuantity = p.minQuantity;
    maxQuantity = p.maxQuantity;
    quantityStep = p.quantityStep;
    manageStock = p.manageStock;
    backOrdered = p.backOrdered;
    backordersAllowed = p.backordersAllowed;
    type = p.type;
    affiliateUrl = p.affiliateUrl;
    variations = p.variations?.toList();
    options = List.from(jsonDecode(jsonEncode(p.options ?? [])));
    idShop = p.idShop;
    shortDescription = p.shortDescription;
    tags = p.tags.toList();
    selectedOptions = p.selectedOptions?.toList();
    addOns = p.addOns?.toList();
    variationProducts = p.variationProducts?.toList();
    vendorAdminProductAttributes = p.vendorAdminProductAttributes.toList();
    imageFeature = p.imageFeature;
    vendorAdminImageFeature = p.vendorAdminImageFeature;
    images = p.images;
    categories = p.categories.toList();
    components = p.components;

    p.defaultAddonsOptions.forEach((key, value) {
      if (value.isNotEmpty) {
        defaultAddonsOptions[key] = value;
      } else {
        defaultAddonsOptions[key] = <String, AddonsOption>{};
      }
    });
  }

  dynamic _getMetaDataValue(String key) {
    var metaData = _getMetaData(key);
    return metaData['value'];
  }

  Product.fromJson(Map parsedJson) : id = parsedJson['id'].toString() {
    try {
      id = parsedJson['id'].toString();
      listingType = false;
      sku = parsedJson['sku'];
      status = parsedJson['status'];
      name = parsedJson['name'] != null
          ? HtmlUnescape().convert(parsedJson['name'])
          : parsedJson['post_title'];
      // name = HtmlUnescape().convert(parsedJson['name']);
      type = parsedJson['type'];
      description = '${parsedJson['description'] ?? ''}';
      shortDescription = parsedJson['short_description'];
      permalink = parsedJson['permalink'];
      price = parsedJson['price'] != null ? parsedJson['price'].toString() : '';

      regularPrice = isNotBlank('${parsedJson['regular_price'] ?? ''}')
          ? parsedJson['regular_price'].toString()
          : null;
      salePrice = isNotBlank('${parsedJson['sale_price'] ?? ''}')
          ? parsedJson['sale_price'].toString()
          : null;

      if (isVariableProduct) {
        onSale = salePrice != null && (parsedJson['on_sale'] == true);
      } else {
        var onSaleByPrice = false;

        if (regularPrice != null && regularPrice != 'null') {
          onSaleByPrice = price != regularPrice &&
              double.parse(regularPrice.toString()) >
                  double.parse(parsedJson['price'].toString());
        }

        onSale = salePrice != null &&
            isNotBlank(parsedJson['price'].toString()) &&
            onSaleByPrice;
      }

      /// In case parsedJson['manage_stock'] = "parent" 😂
      manageStock = parsedJson['manage_stock'] == true;

      inStock =
          parsedJson['in_stock'] ?? parsedJson['stock_status'] == 'instock';
      if (inStock == true && manageStock) {
        inStock =
            (int.tryParse(parsedJson['stock_quantity']?.toString() ?? '0') ??
                    0) >
                0;
      }
      backOrdered = parsedJson['backordered'] ?? false;
      backordersAllowed = (parsedJson['backorders_allowed'] ?? false) ||
          ((parsedJson['backorders'] ?? 'no') != 'no');

      /// In case manage stock is disabled,
      /// customers can still purchase if stockstatus is backordered;
      if (!manageStock && !backordersAllowed) {
        backordersAllowed = backOrdered;
      }

      averageRating =
          double.tryParse(parsedJson['average_rating']?.toString() ?? '0.0') ??
              0.0;
      ratingCount =
          int.tryParse((parsedJson['rating_count'] ?? 0).toString()) ?? 0;
      totalSales =
          int.tryParse((parsedJson['total_sales'] ?? 0).toString()) ?? 0;
      if (parsedJson['date_on_sale_from'] != null) {
        if (parsedJson['date_on_sale_from'] is Map) {
          dateOnSaleFrom = parsedJson['date_on_sale_from']['date'];
        } else {
          dateOnSaleFrom = parsedJson['date_on_sale_from'];
        }
      }

      if (parsedJson['date_on_sale_to'] != null) {
        if (parsedJson['date_on_sale_to'] is Map) {
          dateOnSaleTo = parsedJson['date_on_sale_to']['date'];
        } else {
          dateOnSaleTo = parsedJson['date_on_sale_to'];
        }
      }

      categoryId = parsedJson['categories'] != null &&
              parsedJson['categories'].length > 0
          ? parsedJson['categories'][0]['id'].toString()
          : '0';

      isPurchased = parsedJson['is_purchased'] ?? false;
      isDownloadable = parsedJson['downloadable'];
      // add stock limit
      if (parsedJson['manage_stock'] == true) {
        stockQuantity =
            int.tryParse(parsedJson['stock_quantity']?.toString() ?? '0');
      }

      //minQuantity = parsedJson['meta_data']['']

      if (parsedJson['attributes'] is List) {
        parsedJson['attributes']?.forEach((item) {
          if (item['visible'] ?? true) {
            infors.add(ProductAttribute.fromLocalJson(item));
          }
        });
      }

      if (parsedJson['brands'] is List) {
        try {
          for (var item in parsedJson['brands']) {
            final brand = Brand.fromJson(item);
            brands.add(brand);
          }
        } catch (e, trace) {
          printLog(e);
          printLog(trace);
        }
      }

      /// For Vendor Manager
      if (parsedJson['attributesData'] != null) {
        try {
          parsedJson['attributesData'].forEach((element) =>
              vendorAdminProductAttributes
                  .add(ProductAttribute.fromJson(element)..isActive = true));
        } catch (e) {
          printLog(e);
        }
      }

      if (parsedJson['related_ids'] != null &&
          parsedJson['related_ids'].isNotEmpty) {
        relatedIds = '';
        for (var item in parsedJson['related_ids']) {
          if (relatedIds!.isEmpty) {
            relatedIds = item.toString();
          } else {
            relatedIds = '$relatedIds,$item';
          }
        }
      }
      if (parsedJson['variation_products'] != null) {
        for (var item in parsedJson['variation_products']) {
          vendorAdminVariations.add(VendorAdminVariation.fromJson(item));
        }
      }

      var attributeList = <ProductAttribute>[];

      /// Not check the Visible Flag from variant
      bool? notChecking = kNotStrictVisibleVariant;

      try {
        parsedJson['attributesData']?.forEach((item) {
          if (!notChecking!) {
            notChecking = item['visible'];
          }

          if (notChecking! && item['variation']) {
            final attr = ProductAttribute.fromJson(item);
            attributeList.add(attr);

            /// Custom attributes not appeared in ["attributesData"].
            if (attr.options!.isEmpty) {
              /// Need to take from ["attributes"].
              /// we should compare productAttribute.name == attr.name as the id sometime is 0.
              /// For product custom attributes has space in the attribute name,
              /// sometimes " " become "-" in json['attributes'] but not in json['attributeData'].
              attr.options!.addAll(
                infors
                        .firstWhereOrNull((ProductAttribute productAttribute) =>
                            productAttribute.name != null &&
                            attr.name != null &&
                            (productAttribute.name == attr.name ||
                                productAttribute.name!.toLowerCase() ==
                                    attr.name!.toLowerCase() ||
                                productAttribute.name!.replaceAll('-', ' ') ==
                                    attr.name!.replaceAll('-', ' ')))
                        ?.options
                        ?.map((option) => {'name': option}) ??
                    [],
              );
            }

            for (var option in attr.options!) {
              if (option['slug'] != null && option['slug'] != '') {
                attributeSlugMap[option['slug']] = option['name'];
              }
            }
          }
        });
      }
      // ignore: empty_catches
      catch (e) {}

      attributes = attributeList.toList();

      try {
        var defaultAttributesData = <Attribute>[];
        parsedJson['default_attributes']?.forEach((item) {
          defaultAttributesData.add(Attribute.fromJson(item));
        });
        defaultAttributes = defaultAttributesData.toList();
      }
      // ignore: empty_catches
      catch (e) {}

      var list = <String>[];
      if (parsedJson['images'] != null) {
        for (var item in parsedJson['images']) {
          /// If item is String => Use for Vendor Admin.
          var image = '';
          if (item is String) {
            image = item;
          }
          if (item is Map) {
            image = item['src'];
          }
          if (!list.contains(image)) {
            list.add(image);
          }
        }
      }

      final rawMetaData = parsedJson['meta_data'];
      if (rawMetaData is List) {
        metaData = List<Map>.from(rawMetaData);
      } else {
        metaData = <Map>[];
      }

      /// Mapping the product meta data
      var metaImages = metaData.firstWhere(
        (item) => item['key'] == kProductDetail.productMetaDataKey,
        orElse: () => {},
      );
      if (metaImages.isNotEmpty && metaImages['value'] != null) {
        for (var item in metaImages['value']) {
          list.add(item['url']);
        }
      }

      images = list;
      imageFeature = images.isNotEmpty ? images[0] : null;

      try {
        final tagsData = parsedJson['tags'];
        if (tagsData != null && tagsData is List && tagsData.isNotEmpty) {
          for (var tag in tagsData) {
            tags.add(Tag.fromJson(tag));
          }
        }
      } catch (_) {
        // ignore
      }

      try {
        final categoriesData = parsedJson['categories'];
        if (categoriesData != null &&
            categoriesData is List &&
            categoriesData.isNotEmpty) {
          for (var category in categoriesData) {
            if (category['slug'] != 'uncategorized') {
              categories.add(Category.fromJson(category));
            }
          }
        }
      } catch (_) {
        // ignore
      }

      if (kAdvanceConfig.enableWooCommerceWholesalePrices &&
          ServerConfig().isWooPluginSupported) {
        var loggedInUser = UserBox().userInfo;
        if (loggedInUser != null) {
          var wholesaleSalePriceKey =
              '${loggedInUser.role ?? ''}_wholesale_sale_price';
          if (_hasWholesalePrice(loggedInUser, isSale: true)) {
            var wholesaleSalePrice = _getMetaDataValue(wholesaleSalePriceKey);
            if (wholesaleSalePrice != null) {
              price = wholesaleSalePrice;
              wholesalePrice = price;
            }
          }
          if (wholesalePrice == null) {
            var wholesalePriceKey =
                '${loggedInUser.role ?? ''}_wholesale_price';
            if (_hasWholesalePrice(loggedInUser)) {
              var wholesalePrice = _getMetaDataValue(wholesalePriceKey);
              if (wholesalePrice != null) {
                price = wholesalePrice;
                wholesalePrice = price;
              }
            }
          }
        }
        // if (loggedInUser != null) {
        //   var haveWholesalePriceKey =
        //       '${loggedInUser.role ?? ''}_have_wholesale_price';
        //   var wholesalePriceKey = '${loggedInUser.role ?? ''}_wholesale_price';
        //
        //   var haveWholesalePriceMeta = metaData.firstWhere(
        //     (item) => item['key'] == haveWholesalePriceKey,
        //     orElse: () => {},
        //   );
        //   if (haveWholesalePriceMeta.isNotEmpty &&
        //       haveWholesalePriceMeta['value'] == 'yes') {
        //     var wholesalePriceMeta = metaData.firstWhere(
        //       (item) => item['key'] == wholesalePriceKey,
        //       orElse: () => {},
        //     );
        //     if (wholesalePriceMeta.isNotEmpty &&
        //         wholesalePriceMeta['value'] != null) {
        //       price = wholesalePriceMeta['value'];
        //       wholesalePrice = price;
        //     }
        //   }
        // }

        var filterMetas = metaData
            .where((item) =>
                item['key'] == 'wwpp_product_wholesale_visibility_filter')
            .toList();
        if (filterMetas.isNotEmpty &&
            filterMetas.firstWhereOrNull((e) => e['value'] == 'all') == null) {
          isRestricted = filterMetas.firstWhereOrNull(
                  (e) => e['value'] == (loggedInUser?.role ?? '')) ==
              null;
        }
      }

      ///------For Vendor Admin------///
      if (parsedJson['featured_image'] != null) {
        vendorAdminImageFeature = parsedJson['featured_image'];
      }

      if (parsedJson['featured'] != null) {
        isFeatured = parsedJson['featured'];
      }
      if (parsedJson['category_ids'] != null) {
        if (parsedJson['category_ids'] is Map) {
          parsedJson['category_ids']
              .forEach((k, v) => categoryIds.add(v.toString()));
        }
        if (parsedJson['category_ids'] is List) {
          for (var item in parsedJson['category_ids']) {
            categoryIds.add(item.toString());
          }
        }
      }

      ///------For Vendor Admin------///

      /// get video links, support following plugins
      /// - WooFeature Video: https://wordpress.org/plugins/woo-featured-video/
      ///- Yith Feature Video: https://wordpress.org/plugins/yith-woocommerce-featured-video/
      var video = metaData.firstWhere(
        (item) =>
            item['key'] == 'video_url' ||
            item['key'] == '_video_url' ||
            item['key'] == '_woofv_video_embed',
        orElse: () => {},
      );
      if (video.isNotEmpty && video['value'] != null) {
        videoUrl = video['value'] is String
            ? video['value']
            : video['value']['url'] ?? '';
      }

      /// get mstore video setting to support for videos list layout
      var mVideoUrlMeta = metaData.firstWhere(
        (item) => item['key'] == '_mstore_video_url',
        orElse: () => {},
      );
      if (mVideoUrlMeta.isNotEmpty && mVideoUrlMeta['value'] != null) {
        mVideoUrl = mVideoUrlMeta['value'];
      }

      var mVideoTitleMeta = metaData.firstWhere(
        (item) => item['key'] == '_mstore_video_title',
        orElse: () => {},
      );
      if (mVideoTitleMeta.isNotEmpty && mVideoTitleMeta['value'] != null) {
        mVideoTitle = mVideoTitleMeta['value'];
      }

      var mVideoDescMeta = metaData.firstWhere(
        (item) => item['key'] == '_mstore_video_description',
        orElse: () => {},
      );
      if (mVideoDescMeta.isNotEmpty && mVideoDescMeta['value'] != null) {
        mVideoDesc = mVideoDescMeta['value'];
      }

      affiliateUrl = parsedJson['external_url'];

      var groupedProductList = <int>[];
      parsedJson['grouped_products']?.forEach((item) {
        groupedProductList.add(item);
      });
      groupedProducts = groupedProductList;

      var fileNames = <String?>[];
      var files = <String?>[];

      final rawDownloads = parsedJson['downloads'];
      if (rawDownloads is List) {
        for (var item in List.from(parsedJson['downloads'] ?? [])) {
          fileNames.add(item['name']);
          files.add(item['file']);
        }
      } else if (rawDownloads is Map) {
        for (var item in Map.from(parsedJson['downloads'] ?? {}).values) {
          fileNames.add(item['name']);
          files.add(item['file']);
        }
      }

      this.fileNames = fileNames;
      this.files = files;

      if (parsedJson['meta_data'] != null) {
        for (var item in parsedJson['meta_data']) {
          try {
            if (item['key'] == '_wc_min_max_quantities_max_qty' ||
                item['key'] == '_wcmmq_max_qty') {
              var quantity = int.parse(item['value']);
              quantity == 0 ? maxQuantity = null : maxQuantity = quantity;
            }
          } catch (e) {
            printLog('maxQuantity $e');
          }

          try {
            if (item['key'] == '_wc_min_max_quantities_min_qty' ||
                item['key'] == '_wcmmq_min_qty') {
              var quantity = int.parse(item['value']);
              quantity == 0 ? minQuantity = null : minQuantity = quantity;
            }
          } catch (e) {
            printLog('minQuantity $e');
          }

          try {
            if (item['key'] == '_wc_min_max_quantities_step' ||
                item['key'] == '_wcmmq_step') {
              var step = int.parse(item['value']);
              step == 0 ? quantityStep = null : quantityStep = step;
            }
          } catch (e) {
            printLog('quantityStep $e');
          }

          try {
            if (item['key'] == '_product_addons') {
              /// Sometimes it returns as Map, sometimes as List.
              final List<dynamic> values = item['value'] is Map
                  ? (item['value'] as Map).values.toList()
                  : item['value'];
              final addOnNames = [];
              addOns = [];

              for (var value in values) {
                /// Customer Defined Price (custom_price) doesn't have any options.
                if ((value['options'] != null ||
                        value['type'] == 'custom_text' ||
                        value['type'] == 'custom_price') &&
                    value['field_name'] != null) {
                  final item = ProductAddons.fromJson(value);
                  if (item.name != null && !addOnNames.contains(item.name)) {
                    defaultAddonsOptions[item.name!] = item.defaultOptions;
                    addOns!.add(item);
                    addOnNames.add(item.name);
                  }
                }
              }
            }
          } catch (e) {
            printLog('_product_addons $e');
          }

          if (Services().widget.enableMembershipUltimate) {
            try {
              if (item['key'] == 'ihc_mb_who') {
                isRestricted = !item['value'].toString().contains('all') &&
                    item['value'].toString().isNotEmpty;
              }
            } catch (e) {
              printLog('maxQuantity $e');
            }
          }
        }
      }

      minPrice = parsedJson['min_price'];
      maxPrice = parsedJson['max_price'];
      if (isVariableProduct && parsedJson['variation_products'] != null) {
        try {
          variationProducts = [];
          for (var item in parsedJson['variation_products']) {
            variationProducts!.add(ProductVariation.fromJson(Map.from(item)));
          }
        } catch (e) {
          printLog('variation_products_error ${parsedJson['id']} $e');
        }
      }
    } catch (e, trace) {
      printError(e, trace, '🔴 Get product $name :');
    }

    sizes = List<ExtraSettingsModel>.from(
        parsedJson['sizes']?.map((e) => ExtraSettingsModel.fromJson(e)) ?? []);
    colors = List<ExtraSettingsModel>.from(
        parsedJson['colors']?.map((e) => ExtraSettingsModel.fromJson(e)) ?? []);
  }

  Product.fromJsonStrapi(SerializerProduct model, apiLink)
      : id = model.id.toString() {
    try {
      final globalSale = currentVendor?.globalSale;

      final totalPrice = model.price ?? 0;

      final isGlobalSaleNotEqualZero = globalSale != null && globalSale != 0;
      final isGlobalSalePercentage =
          currentVendor?.isGlobalSalePercentage ?? true;

      final finalPrice = isGlobalSalePercentage
          ? totalPrice * (1 - ((globalSale ?? 0) / 100))
          : totalPrice - (globalSale ?? 0);

      regularPrice = model.price.toString();
      price = model.price.toString();

      final isFinalPriceIsUnderZero = finalPrice <= 0;

      onSale = isGlobalSaleNotEqualZero
          ? isFinalPriceIsUnderZero
              ? false
              : true
          : model.isSale;

      salePrice = isGlobalSaleNotEqualZero
          ? isFinalPriceIsUnderZero
              ? price
              : finalPrice.toString()
          : model.salePrice.toString();

      minQuantitySaleNumber = model.minQuantitySaleNumber;
      quantitySale = model.quantitySale;
      isQuantitySalePercentage = model.isQuantitySalePercentage;

      images = [];
      if (model.images != null) {
        for (var item in model.images!) {
          images.add(item.url ?? '');
        }
      }

      imageFeature = images.isNotEmpty ? images[0] : model.thumbnail?.url ?? '';

      sizes = model.sizes;
      colors = model.colors;

      name = translatedText(textEn: model.title, textAr: model.titleAr);
      nameEn = model.title;
      nameAr = model.titleAr;

      description = translatedText(
          textEn: model.description, textAr: model.descriptionAr);

      inStock = !(model.isOutOfStock ?? false);
      stockQuantity = model.inventory;
      isFeatured = model.isFeatured;
      inventoryEnabled = model.inventoryEnabled;
      isSizeColorInventory = model.isSizeColorInventory;

      averageRating = model.review == null ? 0 : model.review!.toDouble();
      ratingCount = 0;

      productCategory = model.productCategories != null
          ? model.productCategories!.isNotEmpty
              ? model.productCategories?.firstOrNull
              : null
          : null;

      if (model.productCategories != null) {
        categoryId = model.productCategories!.isNotEmpty
            ? model.productCategories![0].id.toString()
            : '0';
      } else {
        categoryId = '0';
      }
    } catch (e, trace) {
      printLog('🔴 Get productERROR $name : $e\n$trace');
    }
  }

  Map<String, dynamic> toJson() {
    log('SelectedSize $selectedSize, SelectedColor: $selectedColor');
    return {
      'listingType': listingType,
      'id': id,
      'sku': sku,
      'name': name,
      'nameEn': nameEn,
      'nameAr': nameAr,
      'description': description,
      'permalink': permalink,
      'price': price,
      'regularPrice': regularPrice,
      'salePrice': salePrice,
      'onSale': onSale,
      'inStock': inStock,
      'averageRating': averageRating,
      'ratingCount': ratingCount,
      'total_sales': totalSales,
      'date_on_sale_from': dateOnSaleFrom,
      'date_on_sale_to': dateOnSaleTo,
      'images': images,
      'imageFeature': imageFeature,
      'attributes': attributes?.map((e) => e.toJson()).toList(),
      'addOns': addOns?.map((e) => e.toJson()).toList(),
      'addonsOptions': selectedOptions?.map((e) => e.toJson()).toList(),
      'categoryId': categoryId,
      'stock_quantity': stockQuantity,
      'idShop': idShop,
      'variations': variations?.map((e) => e.toJson()).toList(),
      'infors': infors.map((e) => e.toJson()).toList(),

      ///----FluxStore Listing----///
      'distance': distance,
      'pureTaxonomies': pureTaxonomies,
      'reviews': reviews,
      'featured': featured,
      'verified': verified,
      'tagLine': tagLine,
      'priceRange': priceRange,
      'categoryName': categoryName,
      'hours': hours,
      'location': location,
      'phone': phone,
      'facebook': facebook,
      'email': email,
      'website': website,
      'skype': skype,
      'whatsapp': whatsapp,
      'youtube': youtube,
      'twitter': twitter,
      'eventDate': eventDate,
      'totalReview': totalReview,
      'lat': lat,
      'long': long,
      'prices': listingMenu?.map((e) {
        if (e is ListingMenu) {
          return e.toJson();
        }
        return e;
      }).toList(),
      'slots': slots?.toJson(),
      'isPurchased': isPurchased,
      'isDownloadable': isDownloadable,
      'type': type,
      'bookingInfo': bookingInfo?.toJson(),
      'options': options,
      'metaData': metaData,
      'selectedSize': selectedSize,
      'selectedColor': selectedColor,
      'sizes': sizes.map((e) => e.toJson()).toList(),
      'colors': colors.map((e) => e.toJson()).toList(),
      'minQuantitySaleNumber': minQuantitySaleNumber,
      'quantitySale': quantitySale,
      'isQuantitySalePercentage': isQuantitySalePercentage,
    };
  }

  Product.fromLocalJson(Map json) : id = json['id'].toString() {
    try {
      listingType = json['listingType'] == true;
      sku = json['sku'];
      name = json['name'];
      nameEn = json['nameEn'];
      nameAr = json['nameAr'];
      description = json['description'];
      permalink = json['permalink'];
      price = json['price'];
      regularPrice = json['regularPrice'];
      salePrice = json['salePrice'];
      selectedSize = json['selectedSize'];
      selectedColor = json['selectedColor'];
      onSale = json['onSale'] ?? false;
      inStock = json['inStock'];
      averageRating = json['averageRating'];
      ratingCount = json['ratingCount'];
      totalSales = json['total_sales'];
      dateOnSaleFrom = json['date_on_sale_from'];
      dateOnSaleTo = json['date_on_sale_to'];
      idShop = json['idShop'];
      var imgs = <String>[];

      if (json['images'] != null) {
        for (var item in json['images']) {
          imgs.add(item);
        }
      }
      images = imgs;
      imageFeature = json['imageFeature'];
      var attrs = <ProductAttribute>[];

      if (json['attributes'] != null) {
        for (var item in json['attributes']) {
          attrs.add(ProductAttribute.fromLocalJson(item));
        }
      }

      if (json['infors'] != null) {
        for (var item in json['infors']) {
          infors.add(ProductAttribute.fromLocalJson(item));
        }
      }

      if (json['addOns'] != null) {
        var addOnsData = <ProductAddons>[];
        for (var item in json['addOns']) {
          if (item['field_name'] != null) {
            addOnsData.add(ProductAddons.fromJson(item));
          }
        }
        addOns = addOnsData;
      }

      if (json['addonsOptions'] != null) {
        var options = <AddonsOption>[];
        for (var item in json['addonsOptions']) {
          options.add(AddonsOption.fromJson(item));
        }
        selectedOptions = options;
      }

      attributes = attrs;
      categoryId = "${json['categoryId']}";
      stockQuantity = json['stock_quantity'];

      isPurchased = json['isPurchased'] ?? false;
      isDownloadable = json['isDownloadable'] ?? false;
      variations = List.from(json['variations'] ?? []).map((variantJson) {
        return ProductVariation.fromLocalJson(variantJson);
      }).toList();
      type = json['type'];

      ///----FluxStore Listing----///

      distance = json['distance'];
      pureTaxonomies = json['pureTaxonomies'];
      reviews = json['reviews'];
      featured = json['featured'];
      verified = json['verified'];
      tagLine = json['tagLine'];
      priceRange = json['priceRange'];
      categoryName = json['categoryName'];
      hours = json['hours'];
      location = json['location'];
      phone = json['phone'];
      facebook = json['facebook'];
      email = json['email'];
      website = json['website'];
      skype = json['skype'];
      whatsapp = json['whatsapp'];
      youtube = json['youtube'];
      twitter = json['twitter'];
      eventDate = json['eventDate'];
      totalReview = json['totalReview'];
      lat = json['lat'];
      long = json['long'];
      listingMenu = json['prices'];
      if (json['bookingInfo'] != null) {
        bookingInfo = BookingModel.fromLocalJson(json['bookingInfo']);
      }
      if (json['options'] != null) {
        options = List<Map>.from(json['options']);
      }
      if (json['metaData'] != null) {
        metaData = List<Map>.from(json['metaData']);
      }

      sizes = json['sizes'] == null
          ? []
          : (json['sizes'] as List)
              .map((e) => e is Map<String, dynamic>
                  ? ExtraSettingsModel.fromJson(e)
                  : null)
              .where((e) => e != null)
              .toList()
              .cast<ExtraSettingsModel>();

      colors = json['colors'] == null
          ? []
          : (json['colors'] as List)
              .map((e) => e is Map<String, dynamic>
                  ? ExtraSettingsModel.fromJson(e)
                  : null)
              .where((e) => e != null)
              .toList()
              .cast<ExtraSettingsModel>();
      minQuantitySaleNumber = json['minQuantitySaleNumber'];
      quantitySale = json['quantitySale'];
      isQuantitySalePercentage = json['isQuantitySalePercentage'];
    } catch (e, trace) {
      printError(e, trace, '[product.dart] Product.fromLocalJson error');
    }
  }

  @override
  String toString() => 'Product { id: $id name: $name type: $type }';

  /// Get productID from mix String productID-ProductVariantID+productAddonOptions
  static String cleanProductID(productString) {
    // In case 1234+https://somelink.com/might-have-dash-character-here
    if (productString.contains('-') && !productString.contains('+')) {
      return productString.split('-')[0].toString();
    } else if (productString.contains('+')) {
      // In case 1234-6789+https://someaddonsoption
      return cleanProductID(productString.split('+')[0].toString());
    } else {
      return productString.toString();
    }
  }

  ///----Copy With----////

  Product copyWith(
      {String? id,
      String? sku,
      String? name,
      String? status,
      String? vendor,
      String? description,
      String? shortDescription,
      String? permalink,
      String? price,
      String? regularPrice,
      String? salePrice,
      String? maxPrice,
      String? minPrice,
      bool? onSale,
      bool? inStock,
      double? averageRating,
      int? totalSales,
      String? dateOnSaleFrom,
      String? dateOnSaleTo,
      int? ratingCount,
      List<String>? images,
      String? imageFeature,
      List<ProductAttribute>? attributes,
      List<Attribute>? defaultAttributes,
      List<ProductAttribute>? infors,
      String? categoryId,
      String? videoUrl,
      List<dynamic>? groupedProducts,
      List<String?>? files,
      int? stockQuantity,
      int? minQuantity,
      int? maxQuantity,
      int? quantityStep,
      bool? manageStock,
      bool? backOrdered,
      String? relatedIds,
      bool? backordersAllowed,
      List<Tag>? tags,
      List<Category>? categories,
      List<Map>? metaData,
      List<ProductAddons>? addOns,
      List<AddonsOption>? selectedOptions,
      List<VendorAdminVariation>? vendorAdminVariations,
      List<ProductVariation>? variationProducts,
      bool? isPurchased,
      bool? isDownloadable,
      String? type,
      String? affiliateUrl,
      List<ProductVariation>? variations,
      List<Map>? options,
      BookingModel? bookingInfo,
      String? idShop,
      bool? isFeatured,
      String? vendorAdminImageFeature,
      List<String>? categoryIds,
      List<ProductAttribute>? vendorAdminProductAttributes,
      String? distance,
      Map? pureTaxonomies,
      List? reviews,
      String? featured,
      bool? verified,
      String? tagLine,
      String? priceRange,
      String? categoryName,
      String? hours,
      String? location,
      String? phone,
      String? facebook,
      String? email,
      String? website,
      String? skype,
      String? whatsapp,
      String? youtube,
      String? twitter,
      String? instagram,
      String? eventDate,
      int? totalReview,
      double? lat,
      double? long,
      List<dynamic>? listingMenu,
      ListingSlots? slots,
      String? selectedSize,
      String? selectedColor,
      List<ExtraSettingsModel>? sizes,
      List<ExtraSettingsModel>? colors,
      bool? isRestricted}) {
    return Product(
      id: id ?? this.id,
      sku: sku ?? this.sku,
      name: name ?? this.name,
      sizes: sizes ?? this.sizes,
      colors: colors ?? this.colors,
      selectedSize: selectedSize ?? this.selectedSize,
      selectedColor: selectedColor ?? this.selectedColor,
      status: status ?? this.status,
      vendor: vendor ?? this.vendor,
      description: description ?? this.description,
      shortDescription: shortDescription ?? this.shortDescription,
      permalink: permalink ?? this.permalink,
      price: price ?? this.price,
      regularPrice: regularPrice ?? this.regularPrice,
      salePrice: salePrice ?? this.salePrice,
      maxPrice: maxPrice ?? this.maxPrice,
      minPrice: minPrice ?? this.minPrice,
      onSale: onSale ?? this.onSale,
      inStock: inStock ?? this.inStock,
      averageRating: averageRating ?? this.averageRating,
      totalSales: totalSales ?? this.totalSales,
      dateOnSaleFrom: dateOnSaleFrom ?? this.dateOnSaleFrom,
      dateOnSaleTo: dateOnSaleTo ?? this.dateOnSaleTo,
      ratingCount: ratingCount ?? this.ratingCount,
      images: images ?? this.images,
      imageFeature: imageFeature ?? this.imageFeature,
      attributes: attributes ?? this.attributes,
      defaultAttributes: defaultAttributes ?? this.defaultAttributes,
      infors: infors ?? this.infors,
      categoryId: categoryId ?? this.categoryId,
      videoUrl: videoUrl ?? this.videoUrl,
      groupedProducts: groupedProducts ?? this.groupedProducts,
      files: files ?? this.files,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      minQuantity: minQuantity ?? this.minQuantity,
      maxQuantity: maxQuantity ?? this.maxQuantity,
      quantityStep: quantityStep ?? this.quantityStep,
      manageStock: manageStock ?? this.manageStock,
      backOrdered: backOrdered ?? this.backOrdered,
      relatedIds: relatedIds ?? this.relatedIds,
      backordersAllowed: backordersAllowed ?? this.backordersAllowed,
      tags: tags ?? this.tags,
      categories: categories ?? this.categories,
      metaData: metaData ?? this.metaData,
      addOns: addOns ?? this.addOns,
      selectedOptions: selectedOptions ?? this.selectedOptions,
      vendorAdminVariations:
          vendorAdminVariations ?? this.vendorAdminVariations,
      variationProducts: variationProducts ?? this.variationProducts,
      isPurchased: isPurchased ?? this.isPurchased,
      isDownloadable: isDownloadable ?? this.isDownloadable,
      type: type ?? this.type,
      affiliateUrl: affiliateUrl ?? this.affiliateUrl,
      variations: variations ?? this.variations,
      options: options ?? this.options,
      bookingInfo: bookingInfo ?? this.bookingInfo,
      idShop: idShop ?? this.idShop,
      isFeatured: isFeatured ?? this.isFeatured,
      vendorAdminImageFeature:
          vendorAdminImageFeature ?? this.vendorAdminImageFeature,
      categoryIds: categoryIds ?? this.categoryIds,
      vendorAdminProductAttributes:
          vendorAdminProductAttributes ?? this.vendorAdminProductAttributes,
      distance: distance ?? this.distance,
      pureTaxonomies: pureTaxonomies ?? this.pureTaxonomies,
      reviews: reviews ?? this.reviews,
      featured: featured ?? this.featured,
      verified: verified ?? this.verified,
      tagLine: tagLine ?? this.tagLine,
      priceRange: priceRange ?? this.priceRange,
      categoryName: categoryName ?? this.categoryName,
      hours: hours ?? this.hours,
      location: location ?? this.location,
      phone: phone ?? this.phone,
      facebook: facebook ?? this.facebook,
      email: email ?? this.email,
      website: website ?? this.website,
      skype: skype ?? this.skype,
      whatsapp: whatsapp ?? this.whatsapp,
      youtube: youtube ?? this.youtube,
      twitter: twitter ?? this.twitter,
      eventDate: eventDate ?? this.eventDate,
      totalReview: totalReview ?? this.totalReview,
      lat: lat ?? this.lat,
      long: long ?? this.long,
      listingMenu: listingMenu ?? this.listingMenu,
      slots: slots ?? this.slots,
      isRestricted: isRestricted ?? this.isRestricted,
    );
  }
}
