import 'package:quiver/strings.dart';

import '../../common/constants.dart';
import '../serializers/payment.dart';

class PaymentMethod {
  String? id;
  String? title;
  String? apiKey;
  String? description;
  bool? enabled;

  PaymentMethod({
    this.id,
    this.title,
    this.apiKey,
    this.description,
    this.enabled,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'apiKey': apiKey,
      'enabled': enabled
    };
  }

  PaymentMethod.fromJson(Map parsedJson) {
    id = parsedJson['id'];
    title = isNotBlank(parsedJson['title'])
        ? parsedJson['title']
        : parsedJson['method_title'];
    description = parsedJson['description'] is String &&
            isNotBlank(parsedJson['description'])
        ? parsedJson['description']
        : null;
    apiKey = parsedJson['api_key'];
    enabled = true;
  }

  PaymentMethod.fromStrapiJson(Map<String, dynamic> parsedJson) {
    var model = SerializerPayment.fromJson(parsedJson);
    try {
      id = model.id.toString();
      title = model.title;
      description = model.description ?? '';
      enabled = true;
      apiKey = model.apiKey;
    } on Exception catch (e, trace) {
      printLog('Error on Payment Models Strapi: $e trace: ${trace.toString()}');
    }
  }

  static String stripeGooglePay = 'stripe_v2_google_pay';
  static String stripeApplePay = 'stripe_v2_apple_pay';
  static String stripeCard = 'stripe_card';

  bool get isStripeGooglePay => id == stripeGooglePay;

  bool get isStripeApplePay => id == stripeApplePay;

  bool get isStripeCard => id == stripeCard;
}
