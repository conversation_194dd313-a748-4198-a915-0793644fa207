import 'dart:developer';

import '../../common/constants.dart';
import '../../common/tools/tools.dart';
import '../entities/delivery_user.dart';
import '../index.dart';
import '../serializers/product.dart';
import 'commission_data.dart';

class ProductItem {
  String? id;
  String? productId;
  String? variationId;
  String? name;
  int? quantity;
  String? total;
  String? totalTax;
  String? featuredImage;
  Map<String, dynamic> addonsOptions = {};
  List<String?> attributes = [];
  DeliveryUser? deliveryUser;
  List<Map<String, dynamic>?> prodOptions = []; // for opencart
  String? storeId;
  String? storeName;
  Product? product;

  CommissionData? commissionData;
  double? commissionTotal;

  String get displayAddonOptions {
    final temp = {};
    if (addonsOptions.isNotEmpty) {
      for (var element in addonsOptions.keys) {
        temp[element] = Tools.getFileNameFromUrl(addonsOptions[element]!);
      }
    }
    return temp.values.map((e) => e.toString().toUpperCase()).join(' - ');
  }

  ProductItem.fromJson(Map parsedJson) {
    try {
      productId = parsedJson['product_id'].toString();
      variationId = parsedJson['variation_id'].toString();
      name = parsedJson['name'];
      quantity = int.tryParse("${parsedJson["quantity"]}") ?? 0;
      total = parsedJson['total'];
      totalTax = parsedJson['total_tax'];
      featuredImage = parsedJson['featured_image'];
      if (parsedJson['featured_image'] != null) {
        featuredImage = parsedJson['featured_image'];
      }

      final productData = parsedJson['product_data'];
      if (productData != null) {
        try {
          product = Product.fromJson(productData);
          featuredImage = product!.imageFeature;
        } catch (e) {
          printLog('Error in product_item.dart - $name: $e');
        }
      }

      featuredImage ??= kDefaultImage;

      final metaData = parsedJson['meta_data'] ?? parsedJson['meta'];
      if (metaData is List) {
        if (parsedJson['product_data'] != null &&
            parsedJson['product_data']['type'] == 'appointment') {
          final Map<String, dynamic>? day = metaData.firstWhere(
              (element) =>
                  element['key'] == 'wc_appointments_field_start_date_day',
              orElse: () => null);
          final Map<String, dynamic>? month = metaData.firstWhere(
              (element) =>
                  element['key'] == 'wc_appointments_field_start_date_month',
              orElse: () => null);
          final Map<String, dynamic>? year = metaData.firstWhere(
              (element) =>
                  element['key'] == 'wc_appointments_field_start_date_year',
              orElse: () => null);
          final Map<String, dynamic>? time = metaData.firstWhere(
              (element) =>
                  element['key'] == 'wc_appointments_field_start_date_time',
              orElse: () => null);
          if (day != null && month != null && year != null && time != null) {
            final dateTime = DateTime.parse(
                "${year['value']}-${Tools.getTimeWith2Digit(month['value'])}-${Tools.getTimeWith2Digit(day['value'])} ${time['value']}");
            var appointmentDate = Tools.convertDateTime(dateTime);
            if (appointmentDate != null) {
              addonsOptions['appointment_date'] = appointmentDate;
            }
          }
        } else {
          addonsOptions = {};

          /// Not from Vendor Admin.
          if (parsedJson['meta'] == null &&
              parsedJson['product_data'] != null) {
            final productMetaData = parsedJson['product_data']?['meta_data'];
            for (var item in productMetaData) {
              if (item['key'] == '_product_addons') {
                for (var element in metaData) {
                  if (element['key'].toString().isNotEmpty &&
                      element['key'].toString().substring(0, 1) != '_') {
                    if (element['value'].toString().isNotEmpty) {
                      addonsOptions[element['key'].toString()] =
                          element['value'].toString();
                    }
                  }
                }
                break;
              }
            }
          }

          /// From Vendor Admin.
          if (parsedJson['meta'] != null) {
            for (var element in metaData) {
              if (element['key'].toString().isNotEmpty &&
                  element['key'].toString().substring(0, 1) != '_') {
                addonsOptions[element['key'].toString()] =
                    element['value'].toString();
              }
            }
          }
        }

        for (var attr in metaData) {
          if (attr['key'] == '_vendor_id') {
            storeId = attr['value'];
            storeName = attr['display_value'];
          }
        }
      }

      /// Custom meta_data. Refer to ticket https://support.inspireui.com/mailbox/tickets/9593
      // if (metaData is List) {
      //   addonsOptions = '';
      //   for (var item in metaData) {
      //     if (['attribute_pa_color'].contains(item['key'])) {
      //       if (addonsOptions!.isEmpty) {
      //         addonsOptions = '${item['value']}';
      //       } else {
      //         addonsOptions = '$addonsOptions,${item['value']}';
      //       }
      //     }
      //   }
      // }

      /// For FluxStore Manager
      id = parsedJson['id'].toString();
      if (parsedJson['delivery_user'] != null) {
        deliveryUser = DeliveryUser.fromJson(parsedJson['delivery_user']);
      }

      if (parsedJson['commission'] != null &&
          parsedJson['commission'].isNotEmpty) {
        commissionData = CommissionData.fromMap(parsedJson['commission']);
        if (commissionData!.commissionFixed.isNotEmpty) {
          commissionTotal = ((double.parse(total!) -
                  double.parse(commissionData!.commissionFixed)))
              .abs();
        } else if (commissionData!.commissionPercent.isNotEmpty) {
          commissionTotal = ((double.parse(total!) -
                      double.parse(commissionData!.commissionPercent) *
                          double.parse(total!)) /
                  100)
              .abs();
        }
      }
    } catch (e, trace) {
      printLog(e.toString());
      printLog(trace.toString());
    }
  }

  Map<String, dynamic> toJson() {
    try {
      return {
        'product_id': productId,
        'id': id,
        'name': name,
        'quantity': quantity,
        'total': total,
        'price': double.parse(total!),
        'featuredImage': featuredImage
      };
    } catch (e, trace) {
      printLog(e.toString());
      printLog(trace.toString());
      return {};
    }
  }

  ProductItem.fromLocalJson(Map parsedJson) {
    productId = "${parsedJson["product_id"]}";
    name = parsedJson['name'];
    quantity = parsedJson['quantity'];
    total = parsedJson['total'].toString();
    featuredImage = parsedJson['featuredImage'];
  }

  ProductItem.fromStrapiJson(SerializerProduct model, {num? price}) {
    log('Size_Price $price, ProductItemJsonPrice ${model.price}, SalePrice ${model.salePrice}');
    try {
      // var model = SerializerProduct.fromJson(parsedJson);
      productId = model.id.toString();
      name = model.title;
      total = price?.toString();
      // ?? model.salePrice.toString();

      var imageList = [];
      if (model.images != null) {
        for (var item in model.images!) {
          imageList.add(item.url ?? '');
        }
      }
      featuredImage =
          imageList.isNotEmpty ? imageList[0] : model.thumbnail?.url ?? '';
    } catch (e, trace) {
      printLog(e.toString());
      printLog(trace.toString());
    }
  }
}

class ProductQuantityModel {
  final int? quantity;
  final num? price;
  final String size;
  final String color;
  final ProductItem? product;
  final SerializerProduct? serializerProduct;

  ProductQuantityModel({
    required this.quantity,
    this.size = '',
    this.color = '',
    this.product,
    this.price,
    this.serializerProduct,
  });

  factory ProductQuantityModel.fromJson(Map<String, dynamic> json) {
    final product =
        json['product'] != null ? ProductItem.fromJson(json['product']) : null;
    final serializerProduct = json['product'] != null
        ? SerializerProduct.fromJson(json['product'])
        : null;

    return ProductQuantityModel(
      quantity: json['quantity'],
      size: json['size'] ?? '',
      color: json['color'] ?? '',
      product: product,
      price: json['price'],
      serializerProduct: serializerProduct,
    );
  }
}
