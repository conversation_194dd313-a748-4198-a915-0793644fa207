import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../common/config.dart';
import '../../../models/entities/product.dart';
import '../../../modules/dynamic_layout/config/product_config.dart';
import '../../../services/services.dart';
import '../action_button_mixin.dart';
import '../quantity_selection/quantity_selection.dart';
import 'cart_icon.dart';

class CartQuantity extends StatefulWidget with ActionButtonMixin {
  final Product product;
  final ProductConfig config;
  final Function(int)? onChangeQuantity;
  final bool showQuantity;
  final bool showCartIcon;
  final double height;
  final double width;

  const CartQuantity({
    super.key,
    required this.product,
    required this.config,
    this.onChangeQuantity,
    this.height = 42.0,
    this.width = 40.0,
    this.showQuantity = false,
    this.showCartIcon = true,
  });

  @override
  State<CartQuantity> createState() => _CartQuantityState();
}

class _CartQuantityState extends State<CartQuantity> {
  var _quantity = 1;

  @override
  Widget build(BuildContext context) {
    var show = widget.showQuantity ||
        (widget.product.canBeAddedToCartFromList(
              enableBottomAddToCart: widget.config.enableBottomAddToCart,
            ) &&
            widget.config.showQuantity &&
            Services().widget.enableShoppingCart(widget.product));

    if (!show) {
      return const SizedBox();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: widget.config.showCartIcon || widget.showCartIcon
            ? MainAxisAlignment.spaceBetween
            : MainAxisAlignment.center,
        children: [
          Expanded(
            child: QuantitySelection(
              height: 30,
              width: widget.width,
              // double.infinity,
              color: Theme.of(context).colorScheme.secondary,
              limitSelectQuantity: kCartDetail['maxAllowQuantity'] ?? 100,
              value: _quantity,
              onChanged: (int value) {
                setState(() {
                  _quantity = value;
                });
                if (widget.onChangeQuantity != null) {
                  widget.onChangeQuantity!(value);
                }
                return true;
              },
              style: QuantitySelectionStyle.style01,
            ),
          ),
          // const Spacer(),
          if (widget.config.showCartIcon && widget.showCartIcon) ...[
            const SizedBox(width: 10),
            InkWell(
                borderRadius: BorderRadius.circular(100),
                onTap: () {
                  widget.addToCart(
                    context,
                    product: widget.product,
                    quantity: _quantity,
                    enableBottomAddToCart: false,
                  );
                },
                child: CircleAvatar(
                    radius: 17,
                    backgroundColor: Theme.of(context).primaryColor,
                    child: const Icon(
                      CupertinoIcons.cart_badge_plus,
                      color: Colors.white,
                      size: 22,
                    ))),
          ]
          // if (widget.config.showCartIcon) ...[
          //   CartIcon(
          //     config: widget.config,
          //     quantity: _quantity,
          //     product: widget.product,
          //   ),
          //   const SizedBox(width: 3),
          // ],
        ],
      ),
    );
  }
}
