import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../generated/l10n.dart';
import '../../../models/index.dart' show Product, ProductModel;
import '../../../services/index.dart' show ServerConfig, Services;
import '../../../widgets/common/background_color_widget.dart';
import '../../../widgets/product/product_quilted_card.dart';
import '../config/product_config.dart';
import '../helper/header_view.dart';
import '../helper/helper.dart';
import 'pinterest_card.dart';

class PinterestLayout extends StatefulWidget {
  final ProductConfig config;

  const PinterestLayout({required this.config});

  @override
  State<PinterestLayout> createState() => _PinterestLayoutState();
}

class _PinterestLayoutState extends State<PinterestLayout> {
  final Services _service = Services();
  List<Product> _products = [];
  int _page = 0;
  bool _isEnd = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    //TODO-AllProducts
    _loadProduct().then((value) {
      if (_products.isNotEmpty) {
        _products.shuffle();

        _products = _products.take(6).toList();
      }
    });
  }

  Future<void> _loadProduct() async {
    if (_isLoading) {
      return;
    }
    _isLoading = true;
    var config = widget.config.toJson();
    _page = _page + 1;
    config['page'] = _page;
    config['limit'] = 10;

    List<Product>? newProducts;
    var jsonData = widget.config.jsonData;
    if (_page == 1 && jsonData is Map && jsonData['data'] != null) {
      newProducts = _service.api.productsFromJsonData(jsonData['data']);
    }
    newProducts ??=
        await (_service.api.fetchProductsLayout(config: config)) ?? [];
    var isExisted = newProducts.isNotEmpty &&
        _products.indexWhere((o) => o.id == newProducts?[0].id) > -1;
    if (!isExisted) {
      _products = [..._products, ...newProducts];
      if (newProducts.length < config['limit']) {
        _isEnd = true;
      }
    }
    _isLoading = false;

    if (mounted) {
      setState(() {});
    }
  }

  ProductConfig get config => widget.config;

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.sizeOf(context).width > 600;
    // Helper.isTablet(MediaQuery.of(context));
    final isDesktop = Layout.isDisplayDesktop(context);

    var column = isTablet ? 3 : 2;

    //BUG: has problem when scroll the list products
    final size = MediaQuery.sizeOf(context);
    final padding = MediaQuery.paddingOf(context);
    return BackgroundColorWidget(
      enable: config.enableBackground,
      child: LimitedBox(
        maxHeight: size.height - padding.vertical,
        child: CustomScrollView(
          cacheExtent: 1000,
          physics: const NeverScrollableScrollPhysics(),
          slivers: <Widget>[
            if (config.name?.isNotEmpty == true || config.showSeeAll)
              SliverPersistentHeader(
                pinned: true,
                delegate: _SliverAppBarDelegate(
                  minHeight: 60.0,
                  maxHeight: 60.0,
                  child: HeaderView(
                    headerText: widget.config.name ?? '',
                    showSeeAll: widget.config.showSeeAll &&
                        !ServerConfig().isListingType,
                    callback: () => ProductModel.showList(
                      config: widget.config.jsonData,
                    ),
                  ),
                ),
              ),
            SliverPadding(
              padding: EdgeInsets.symmetric(
                vertical: widget.config.vPadding,
                horizontal: widget.config.hPadding,
              ),
              sliver: SliverMasonryGrid.count(
                crossAxisCount: 2,
                // column,
                mainAxisSpacing: 16.0,
                crossAxisSpacing: 16.0,

                childCount: _products.length,
                itemBuilder: (context, index) {
                  final product = _products[index];
                  if (widget.config.cardDesign.isQuiltedCard) {
                    return ProductQuiltedCard(
                      item: product,
                      width: size.width * 2 / 3,
                      config: widget.config,
                    );
                  }

                  return PinterestCard(
                    item: _products[index],
                    width: size.width / (isDesktop ? 3 : 2),
                    config: widget.config,
                  );
                },
                // staggeredTileBuilder: (index) => const StaggeredTile.fit(2),
              ),
            ),
            // SliverToBoxAdapter(
            //   child: _isEnd
            //       ? const SizedBox()
            //   // Padding(
            //   //   padding: const EdgeInsets.all(32.0),
            //   //   child: Center(child: Text(S.of(context).noData)),
            //   // )
            //       : VisibilityDetector(
            //     key: const Key('loading_visible'),
            //     onVisibilityChanged: (VisibilityInfo info) =>
            //         _loadProduct(),
            //     child: Center(
            //       child: Text(S.of(context).loading),
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}

// import 'package:flutter/material.dart';
// import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
// import 'package:visibility_detector/visibility_detector.dart';
//
// import '../../../generated/l10n.dart';
// import '../../../models/index.dart' show Product, ProductModel;
// import '../../../services/index.dart' show ServerConfig, Services;
// import '../../../widgets/common/background_color_widget.dart';
// import '../../../widgets/product/product_quilted_card.dart';
// import '../config/product_config.dart';
// import '../helper/header_view.dart';
// import '../helper/helper.dart';
// import 'pinterest_card.dart';
//
// class PinterestLayout extends StatefulWidget {
//   final ProductConfig config;
//
//   const PinterestLayout({required this.config});
//
//   @override
//   State<PinterestLayout> createState() => _PinterestLayoutState();
// }
//
// class _PinterestLayoutState extends State<PinterestLayout> {
//   final Services _service = Services();
//   List<Product> _products = [];
//   int _page = 0;
//   bool _isEnd = false;
//   bool _isLoading = false;
//
//   @override
//   void initState() {
//     super.initState();
//
//     //TODO-AllProducts
//     _loadProduct().then((value) {
//       if (_products.isNotEmpty) {
//         _products.shuffle();
//         _products = _products.take(10).toList();
//       }
//     });
//   }
//
//   Future<void> _loadProduct() async {
//     if (_isLoading) {
//       return;
//     }
//     _isLoading = true;
//     var config = widget.config.toJson();
//     _page = _page + 1;
//     config['page'] = _page;
//     config['limit'] = 10;
//
//     List<Product>? newProducts;
//     var jsonData = widget.config.jsonData;
//     if (_page == 1 && jsonData is Map && jsonData['data'] != null) {
//       newProducts = _service.api.productsFromJsonData(jsonData['data']);
//     }
//     newProducts ??=
//         await (_service.api.fetchProductsLayout(config: config)) ?? [];
//     var isExisted = newProducts.isNotEmpty &&
//         _products.indexWhere((o) => o.id == newProducts?[0].id) > -1;
//     if (!isExisted) {
//       _products = [..._products, ...newProducts];
//       if (newProducts.length < config['limit']) {
//         _isEnd = true;
//       }
//     }
//     _isLoading = false;
//
//     if (mounted) {
//       setState(() {});
//     }
//   }
//
//   ProductConfig get config => widget.config;
//
//   @override
//   Widget build(BuildContext context) {
//     final isTablet = MediaQuery.sizeOf(context).width > 600;
//     // Helper.isTablet(MediaQuery.of(context));
//     final isDesktop = Layout.isDisplayDesktop(context);
//
//     var column = isTablet ? 3 : 2;
//
//     //BUG: has problem when scroll the list products
//     final size = MediaQuery.sizeOf(context);
//     final padding = MediaQuery.paddingOf(context);
//     return BackgroundColorWidget(
//       enable: config.enableBackground,
//       child: LimitedBox(
//         maxHeight: size.height - padding.vertical,
//         child: CustomScrollView(
//           cacheExtent: 1000,
//           physics: const NeverScrollableScrollPhysics(),
//           slivers: <Widget>[
//             if (config.name?.isNotEmpty == true || config.showSeeAll)
//               SliverPersistentHeader(
//                 pinned: true,
//                 delegate: _SliverAppBarDelegate(
//                   minHeight: 60.0,
//                   maxHeight: 60.0,
//                   child: HeaderView(
//                     headerText: widget.config.name ?? '',
//                     showSeeAll: widget.config.showSeeAll && !ServerConfig().isListingType,
//                     callback: () => ProductModel.showList(
//                       config: widget.config.jsonData,
//                     ),
//                   ),
//                 ),
//               ),
//             SliverPadding(
//               padding: EdgeInsets.symmetric(
//                 vertical: widget.config.vPadding,
//                 horizontal: widget.config.hPadding,
//               ),
//               sliver: SliverMasonryGrid.count(
//                 crossAxisCount: column,
//                 mainAxisSpacing: 16.0,
//                 crossAxisSpacing: 16.0,
//                 childCount: _products.length,
//                 itemBuilder: (context, index) {
//                   final product = _products[index];
//                   if (widget.config.cardDesign.isQuiltedCard) {
//                     return ProductQuiltedCard(
//                       item: product,
//                       width: size.width * 2 / 3,
//                       config: widget.config,
//                     );
//                   }
//
//                   return PinterestCard(
//                     item: _products[index],
//                     width: size.width / (isDesktop ? 3 : 2),
//                     config: widget.config,
//                   );
//                 },
//                 // staggeredTileBuilder: (index) => const StaggeredTile.fit(2),
//               ),
//             ),
//             SliverToBoxAdapter(
//               child: _isEnd
//                   ? Padding(
//                 padding: const EdgeInsets.all(32.0),
//                 child: Center(child: Text(S.of(context).noData)),
//               )
//                   : VisibilityDetector(
//                 key: const Key('loading_visible'),
//                 onVisibilityChanged: (VisibilityInfo info) =>
//                     _loadProduct(),
//                 child: Center(
//                   child: Text(S.of(context).loading),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
//   final double minHeight;
//   final double maxHeight;
//   final Widget child;
//
//   _SliverAppBarDelegate({
//     required this.minHeight,
//     required this.maxHeight,
//     required this.child,
//   });
//
//   @override
//   double get minExtent => minHeight;
//
//   @override
//   double get maxExtent => maxHeight;
//
//   @override
//   Widget build(
//       BuildContext context, double shrinkOffset, bool overlapsContent) {
//     return SizedBox.expand(child: child);
//   }
//
//   @override
//   bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
//     return maxHeight != oldDelegate.maxHeight ||
//         minHeight != oldDelegate.minHeight ||
//         child != oldDelegate.child;
//   }
// }
//
// // import 'package:flutter/material.dart';
// // import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
// // import 'package:visibility_detector/visibility_detector.dart';
// //
// // import '../../../generated/l10n.dart';
// // import '../../../models/index.dart' show Product, ProductModel;
// // import '../../../services/index.dart' show ServerConfig, Services;
// // import '../../../widgets/common/background_color_widget.dart';
// // import '../../../widgets/product/product_quilted_card.dart';
// // import '../config/product_config.dart';
// // imp
// // //
// // // class PinterestLayout extends StatefulWidget {
// // //   final ProductConfig config;
// // //
// // //   const PinterestLayout({required this.config});
// // //
// // //   @override
// // //   State<PinterestLayout> createState() => _PinterestLayoutState();
// // // }
// // //
// // // class _PinterestLayoutState extends State<PinterestLayout> {
// // //   final Services _service = Services();
// // //   List<Product> _products = [];
// // //   int _page = 0;
// // //   bool _isEnd = false;
// // //   bool _isLoading = false;
// // //
// // //   @override
// // //   void initState() {
// // //     super.initState();
// // //
// // //     //TODO-AllProducts
// // //     _loadProduct().then((value) {
// // //       if (_products.isNotEmpty) {
// // //         _products.shuffle();
// // //         _products = _products.take(10).toList();
// // //       }
// // //     });
// // //   }
// // //
// // //   Future<void> _loadProduct() async {
// // //     if (_isLoading) {
// // //       return;
// // //     }
// // //     _isLoading = true;
// // //     var config = widget.config.toJson();
// // //     _page = _page + 1;
// // //     config['page'] = _page;
// // //     config['limit'] = 10;
// // //
// // //     List<Product>? newProducts;
// // //     var jsonData = widget.config.jsonData;
// // //     if (_page == 1 && jsonData is Map && jsonData['data'] != null) {
// // //       newProducts = _service.api.productsFromJsonData(jsonData['data']);
// // //     }
// // //     newProducts ??=
// // //         await (_service.api.fetchProductsLayout(config: config)) ?? [];
// // //     var isExisted = newProducts.isNotEmpty &&
// // //         _products.indexWhere((o) => o.id == newProducts?[0].id) > -1;
// // //     if (!isExisted) {
// // //       _products = [..._products, ...newProducts];
// // //       if (newProducts.length < config['limit']) {
// // //         _isEnd = true;
// // //       }
// // //     }
// // //     _isLoading = false;
// // //
// // //     if (mounted) {
// // //       setState(() {});
// // //     }
// // //   }
// // //
// // //   ProductConfig get config => widget.config;
// // //
// // //   @override
// // //   Widget build(BuildContext context) {
// // //     final isTablet = MediaQuery.sizeOf(context).width > 600;
// // //     // Helper.isTablet(MediaQuery.of(context));
// // //     final isDesktop = Layout.isDisplayDesktop(context);
// // //
// // //     var column = isTablet ? 3 : 2;
// // //
// // //     //BUG: has problem when scroll the list products
// // //     final size = MediaQuery.sizeOf(context);
// // //     final padding = MediaQuery.paddingOf(context);
// // //     return BackgroundColorWidget(
// // //       enable: config.enableBackground,
// // //       child: LimitedBox(
// // //         maxHeight: size.height - padding.vertical,
// // //         child: CustomScrollView(
// // //           cacheExtent: 1000,
// // //           physics: const NeverScrollableScrollPhysics(),
// // //           slivers: <Widget>[
// // //             if (config.name?.isNotEmpty == true || config.showSeeAll)
// // //               SliverToBoxAdapter(
// // //                 child: HeaderView(
// // //                   headerText: widget.config.name ?? '',
// // //                   showSeeAll:
// // //                       widget.config.showSeeAll && !ServerConfig().isListingType,
// // //                   callback: () => ProductModel.showList(
// // //                     config: widget.config.jsonData,
// // //                   ),
// // //                 ),
// // //               ),
// // //             SliverPadding(
// // //               padding: EdgeInsets.symmetric(
// // //                 vertical: widget.config.vPadding,
// // //                 horizontal: widget.config.hPadding,
// // //               ),
// // //               sliver: SliverMasonryGrid.count(
// // //                 crossAxisCount: column,
// // //                 mainAxisSpacing: 16.0,
// // //                 crossAxisSpacing: 16.0,
// // //                 childCount: _products.length,
// // //                 itemBuilder: (context, index) {
// // //                   final product = _products[index];
// // //                   if (widget.config.cardDesign.isQuiltedCard) {
// // //                     return ProductQuiltedCard(
// // //                       item: product,
// // //                       width: size.width * 2 / 3,
// // //                       config: widget.config,
// // //                     );
// // //                   }
// // //
// // //                   return PinterestCard(
// // //                     item: _products[index],
// // //                     width: size.width / (isDesktop ? 3 : 2),
// // //                     config: widget.config,
// // //                   );
// // //                 },
// // //                 // staggeredTileBuilder: (index) => const StaggeredTile.fit(2),
// // //               ),
// // //             ),
// // //             SliverToBoxAdapter(
// // //               child: _isEnd
// // //                   ? Padding(
// // //                       padding: const EdgeInsets.all(32.0),
// // //                       child: Center(child: Text(S.of(context).noData)),
// // //                     )
// // //                   : VisibilityDetector(
// // //                       key: const Key('loading_visible'),
// // //                       onVisibilityChanged: (VisibilityInfo info) =>
// // //                           _loadProduct(),
// // //                       child: Center(
// // //                         child: Text(S.of(context).loading),
// // //                       ),
// // //                     ),
// // //             ),
// // //           ],
// // //         ),
// // //       ),
// // //     );
// // //   }
// // // }ort '../helper/header_view.dart';
// // import '../helper/helper.dart';
// // import 'pinterest_card.dart';
