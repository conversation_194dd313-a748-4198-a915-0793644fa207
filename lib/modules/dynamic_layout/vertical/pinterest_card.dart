import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../common/tools.dart';
import '../../../models/index.dart' show Product, CartModel;
import '../../../services/service_config.dart';
import '../../../services/services.dart';
import '../../../widgets/common/start_rating.dart';
import '../../../widgets/product/action_button_mixin.dart';
import '../../../widgets/product/index.dart';
import '../../../widgets/product/widgets/cart_button_with_quantity.dart';
import '../config/product_config.dart';

class PinterestCard extends StatefulWidget with ActionButtonMixin {
  final Product item;
  final width;
  final marginRight;
  final kSize size;
  final ProductConfig config;

  const PinterestCard(
      {required this.item,
      this.width,
      this.size = kSize.medium,
      this.marginRight = 10.0,
      required this.config});

  @override
  State<PinterestCard> createState() => _PinterestCardState();
}

class _PinterestCardState extends State<PinterestCard> with ActionButtonMixin {
  int _quantity = 1;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isTablet = MediaQuery.sizeOf(context).width > 600;

    // Helper.isTablet(MediaQuery.of(context));

    var titleFontSize = isTablet ? 24.0 : 14.0;
    var starSize = isTablet ? 20.0 : 10.0;
    var showCart = widget.config.showCartIcon &&
        Services().widget.enableShoppingCart(widget.item);

    return GestureDetector(
      onTap: () =>
          onTapProduct(context, product: widget.item, config: widget.config),
      child: Container(
        color: Colors.transparent,
        // Theme.of(context).cardColor,
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                ImageResize(
                  url: widget.item.imageFeature,
                  height: 125.0,
                  width: widget.width,
                  size: kSize.medium,
                  isResize: true,
                  fit: BoxFit.contain,
                ),
                if (!widget.config.showOnlyImage)
                  Container(
                    alignment: Alignment.topLeft,
                    padding: const EdgeInsets.only(
                      top: 10,
                      left: 8,
                      right: 8,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        if (!widget.config.hideTitle) ...[
                          Text(widget.item.name!,
                              style: TextStyle(
                                fontSize: titleFontSize,
                              ),
                              maxLines: 1),
                          const SizedBox(height: 6),
                        ],
                        StoreName(
                            product: widget.item,
                            hide: widget.config.hideStore),
                        widget.item.tagLine != null
                            ? Text(
                                widget.item.tagLine.toString(),
                                maxLines: 1,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontStyle: FontStyle.italic,
                                ),
                              )
                            : const SizedBox(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            ProductPricing(
                              product: widget.item,
                              hide: widget.config.hidePrice,
                              fromWebLayout: false,
                            ),
                            // if (!widget.config.hidePrice) ...[
                            //   const SizedBox(height: 6),
                            //   Text(priceProduct,
                            //       style: TextStyle(
                            //           color: theme.colorScheme.secondary)),
                            // ],
                            const SizedBox(
                              height: 4,
                            ),
                            // Selector<CartModel, int>(
                            //   selector: (context, cartModel) =>
                            //       cartModel.productsInCart[widget.item.id] ?? 0,
                            //   builder: (context, quantity, child) {
                            //     return CartButtonWithQuantity(
                            //       quantity: quantity,
                            //       borderRadiusValue:
                            //           widget.config.cartIconRadius,
                            //       increaseQuantityFunction: () {
                            //         // final minQuantityNeedAdd =
                            //         //     widget.item.getMinQuantity();
                            //         // var quantityWillAdd = 1;
                            //         // if (quantity == 0 &&
                            //         //     minQuantityNeedAdd > 1) {
                            //         //   quantityWillAdd = minQuantityNeedAdd;
                            //         // }
                            //         addToCart(
                            //           context,
                            //           quantity: 1,
                            //           product: widget.item,
                            //           enableBottomAddToCart:
                            //               widget.config.enableBottomAddToCart,
                            //         );
                            //       },
                            //       decreaseQuantityFunction: () {
                            //         if (quantity <= 0) return;
                            //         updateQuantity(
                            //           context: context,
                            //           quantity: quantity - 1,
                            //           product: widget.item,
                            //         );
                            //       },
                            //     );
                            //   },
                            // ),
                            // if (widget.config.showStockStatus)
                            // Row(
                            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            //   children: [
                            //     // StockStatus(product: widget.item, config: widget.config),
                            //     Expanded(
                            //       child: CartQuantity(
                            //         product: widget.item,
                            //         config: widget.config,
                            //         showCartIcon: false,
                            //         onChangeQuantity: (val) {
                            //           setState(() {
                            //             _quantity = val;
                            //           });
                            //         },
                            //       ),
                            //     ),
                            //     // if (showCart &&
                            //     //     !widget.item.isEmptyProduct() &&
                            //     //     !ServerConfig().isListingType) ...[
                            //     //   Align(
                            //     //     alignment: context.isRtl
                            //     //         ? Alignment.centerLeft
                            //     //         : Alignment.centerRight,
                            //     //     child: CartIcon(
                            //     //         product: widget.item,
                            //     //         config: widget.config,
                            //     //         quantity: _quantity),
                            //     //   ),
                            //     // ],
                            //   ],
                            // ),
                            CartQuantity(
                              height: 50,
                              width: widget.width - 150,
                              product: widget.item,
                              config: widget.config,
                              showCartIcon: true,
                              onChangeQuantity: (val) {
                                setState(() {
                                  _quantity = val;
                                });
                              },
                            ),
                            // const SizedBox(height: 6),
                            //
                            // CartButton(
                            //   product: widget.item,
                            //   fullWidth: true,
                            //   hide: !widget.item.canBeAddedToCartFromList(
                            //     enableBottomAddToCart: false,
                            //     // widget.config.enableBottomAddToCart
                            //   ),
                            //   enableBottomAddToCart: false,
                            //   // widget.config.enableBottomAddToCart,
                            //   quantity: _quantity,
                            // ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            if (widget.config.enableRating)
                              Expanded(
                                child: SmoothStarRating(
                                    allowHalfRating: true,
                                    starCount: 5,
                                    label: Text(
                                      '${widget.item.ratingCount}',
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                    rating: widget.item.averageRating ?? 0.0,
                                    size: starSize,
                                    color: theme.primaryColor,
                                    borderColor: theme.primaryColor,
                                    spacing: 0.0),
                              ),
                            // if (showCart &&
                            //     !item.isEmptyProduct() &&
                            //     !ServerConfig().isListingType) ...[
                            //   Align(
                            //     alignment: context.isRtl
                            //         ? Alignment.centerLeft
                            //         : Alignment.centerRight,
                            //     child: CartIcon(product: item, config: config),
                            //   ),
                            //   const SizedBox(height: 40)
                            // ]
                          ],
                        )
                      ],
                    ),
                  )
              ],
            ),
            if (widget.config.showHeart && !widget.item.isEmptyProduct())
              Positioned(
                top: 0,
                right: 0,
                child: HeartButton(product: widget.item, size: 18),
              ),
            Positioned.directional(
              start: 8,
              top: 8,
              textDirection: Directionality.of(context),
              child: ProductOnSale(
                product: widget.item,
                config: ProductConfig.empty()..hMargin = 0,
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  // Colors.black,
                  borderRadius: BorderRadius.all(
                    Radius.circular(4),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
